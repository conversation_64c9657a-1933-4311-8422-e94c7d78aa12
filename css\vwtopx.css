@font-face {
  font-family: '思源宋体';
  src: url(https://ztimg.hefei.cc/static/common/fonts/思源宋体.otf);
}
@font-face {
  font-family: 'HANCHANSHUTI';
  src: url(../HANCHANSHUTI·LONGCANG.OTF);
}
body,
div,
h1,
h2,
h3,
h4,
h5,
h6,
html,
li,
p,
span {
  font-size: 2vh;
}
.musicbtn {
  width: 6vh;
  height: 6vh;
  top: 2vh;
  right: 2vh;
  background-image: url(../img/music.png);
  z-index: 11;
}
.logo {
  width: 23vh;
  position: absolute;
  top: 4vh;
  left: 2vh;
  z-index: 11;
}
.warp {
  margin: 0 auto;
  min-height: 102vh;
  height: 100vh;
  width: 60vh;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: '思源宋体';
  background: linear-gradient(#107cb0, #48cdf4);
}
.warp .swipe_container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.warp .page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  color: #fff;
  background: url(../img/bj.jpg) no-repeat center center / 100% auto;
}
.warp .page .title {
  margin-top: 3vh;
  margin-right: 0vh;
  width: 55vh;
  z-index: 2;
}
.warp .page .title2 {
  width: 60vh;
  z-index: 2;
}
.warp .page .button {
  margin-top: 2vh;
  width: 27vh;
}
.warp .page .main {
  width: 60vh;
}
.warp .page .button3 {
  width: 27vh;
  position: absolute;
  bottom: 6vh;
}
.warp .page .stit {
  width: 45vh;
}
.warp .page .form {
  margin-top: -6vh;
  width: 48vh;
  min-height: 40vh;
  padding-bottom: 4vh;
  background: #FFF8D9;
  border-radius: 2vh;
  box-shadow: 1vh 1vh 0 rgba(3, 133, 178, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.warp .page .form .form_item {
  margin-top: 4vh;
  width: 39vh;
  border: 1px solid #1B1B1B;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}
.warp .page .form .form_item label {
  color: #0086AC;
  font-weight: bold;
  flex: 1;
  border-right: 1px solid #1B1B1B;
  background: #E8F8FF;
  display: flex;
  align-items: center;
  justify-content: space-around;
  text-align: justify;
  text-align-last: justify;
  height: 100%;
  padding: 0 1vh;
}
.warp .page .form .form_item select,
.warp .page .form .form_item input {
  width: 28vh;
  height: 4vh;
  flex-shrink: 0;
  color: #000;
  border: none;
  outline: none;
  background: none;
  text-align: center;
  padding: 0 1vh;
}
.warp .page .form .form_item textarea {
  width: 28vh;
  height: 4vh;
  min-height: 4vh;
  max-height: 18vh;
  flex-shrink: 0;
  color: #000;
  border: none;
  outline: none;
  background: none;
  text-align: center;
  resize: none;
  overflow: hidden;
  line-height: 1.4;
  padding: 0 1vh;
  box-sizing: border-box;
}
.warp .page .form .form_item select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.warp .page .form .form_item.required::before {
  content: '*';
  font-size: 2vh;
  font-weight: bold;
  color: red;
  position: absolute;
  right: 1vh;
}
.warp .page .form .form_item.not_required::before {
  content: '选填';
  color: #B3AFAF;
  position: absolute;
  right: 1vh;
}
.warp .page .form2 {
  margin-top: -10vh;
  width: 48vh;
  min-height: 40vh;
  padding-bottom: 4vh;
  background: #FFF8D9;
  border-radius: 2vh;
  box-shadow: 1vh 1vh 0 rgba(3, 133, 178, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.warp .page .form2 .stit2 {
  margin-top: 4vh;
  margin-bottom: 1vh;
  width: 19vh;
}
.warp .page .form2 .form_item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 5vh;
}
.warp .page .form2 .form_item label {
  width: 10vh;
  height: 3vh;
  background: #FFA200;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.warp .page .form2 .form_item label span {
  font-size: 2vh;
}
.warp .page .form2 .form_item .content {
  width: 27vh;
  min-height: 5vh;
  max-height: 12vh;
  overflow-y: auto;
  border-bottom: 1px solid #000;
  margin-left: 4vh;
  color: #000;
  display: flex;
  justify-content: center;
  font-weight: bold;
  text-align: justify;
}
.warp .page .upload {
  margin-top: 2vh;
  width: 48vh;
  height: 21vh;
  background: #FFF8D9;
  box-shadow: 1vh 1vh 0 rgba(3, 133, 178, 0.9);
  border-radius: 2vh;
  border: 1px solid #1B1B1B;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.warp .page .upload .add_area {
  width: 46vh;
  height: 20vh;
  background: #FFFDF4;
  border-radius: 2vh;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.warp .page .upload .add_area .cross {
  margin-top: -3vh;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}
.warp .page .upload .add_area .cross::before {
  content: '';
  position: absolute;
  width: 6vh;
  height: 1vh;
  background: #B3AFAF;
}
.warp .page .upload .add_area .cross::after {
  content: '';
  position: absolute;
  width: 1vh;
  height: 6vh;
  background: #B3AFAF;
}
.warp .page .upload .add_area .cross .p1 {
  margin-top: 15vh;
  color: #B3AFAF;
  font-size: 2vh;
}
.warp .page .upload .add_area .image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.warp .page .button6 {
  width: 23vh;
  margin-top: 4vh;
}
.warp .bj2 {
  background: url(../img/bj2.jpg) no-repeat center center / 100% auto;
}
.blur {
  filter: blur(1vh);
}
.fc {
  justify-content: center;
}
.mask {
  z-index: 10;
  position: fixed;
  top: 0;
  left: 0;
  min-height: 100vh;
  width: 60vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(0, 0, 0, 0.6);
  transform: translateX(-50%);
  left: 50%;
  color: #fff;
}
.mask .popup {
  margin-top: -1vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.mask .popup .back {
  width: 18vh;
  position: absolute;
  bottom: -9vh;
}
.mask .popup1 {
  margin-top: -6vh;
  width: 45vh;
  height: 40vh;
  background: url(../img/popup1.png) no-repeat center top / 100% 100%;
}
.blink-2 {
  animation: blink-2 1s linear infinite both;
}
@keyframes blink-2 {
  0%,
  10%,
  90%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.2;
  }
}
@keyframes flash {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes flash2 {
  0%,
  99% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes sc {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(0.7) translateY(-6vh);
  }
}
@keyframes pulsate-bck2 {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
