<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0,minimal-ui:ios" name="viewport">
    <title>旭辉盛夏玩家招募令</title>
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/style.min.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/animate.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/<EMAIL>">
    <link rel="stylesheet" href="css/index.css?v={php} echo mt_rand(1000,999999);{/php}">
    <script defer src="css/polyfill.js?v={php} echo mt_rand(1000,999999);{/php}"></script>
    <style>
        [v-cloak] {
            display: none;
        }
    </style>
</head>

<body>
    <div id="app" class="warp" v-cloak>
        <img src="img/logo.png" class="logo">
        <div class="musicbtn" :class="{ on:on }"  @click="bgClick"></div>
        <van-swipe vertical class="swipe_container" ref="swipeRef" :show-indicators="false" :loop="false" :touchable="false">
            <van-swipe-item>
                <div class="page fc" :class="{blur:show}" v-if="page!==8">
                    <img class="title animate__animated animate__zoomIn" src="img/title.png">
                    <img src="img/button1.png" class="button animate__animated animate__fadeInLeft" @click="start">
                    <img src="img/button2.png" class="button animate__animated animate__fadeInRight" @click="page=8" v-if="startData.form.name">
                    <img src="img/button2_2.png" class="button animate__animated animate__fadeInRight" @click="showTip" v-if="!startData.form.name">
                </div>
                <div class="page bj2 fc" :class="{blur:show}" v-if="page===8">
                    <img class="title2 animate__animated animate__zoomIn" src="img/title2.png">
                    <div class="form2">
                        <img src="img/stit2.png" class="stit2">
                        <div class="form_item">
                            <label>
                                <span>姓</span>
                                <span>名</span>
                            </label>
                            <div class="content">{{startData.form.name}}</div>
                        </div>
                        <div class="form_item">
                            <label>
                                <span>性</span>
                                <span>别</span>
                            </label>
                            <div class="content">{{startData.form.sex}}</div>
                        </div>
                        <div class="form_item">
                            <label>
                                <span>年</span>
                                <span>龄</span>
                            </label>
                            <div class="content">{{startData.form.age}}</div>
                        </div>
                        <div class="form_item">
                            <label>
                                <span>联</span>
                                <span>系</span>
                                <span>方</span>
                                <span>式</span>
                            </label>
                            <div class="content">{{startData.form.phone}}</div>
                        </div>
                        <div class="form_item">
                            <label>
                                <span>工</span>
                                <span>作</span>
                                <span>经</span>
                                <span>验</span>
                            </label>
                            <div class="content">{{startData.form.work}}</div>
                        </div>
                    </div>
                    <div class="upload">
                        <div class="add_area">
                            <img :src="startData.form.image" class="image" v-if="startData.form.image">
                        </div>
                    </div>
                    <img src="img/button8.png" class="button6" @click="edit">
                </div>
            </van-swipe-item>
            <van-swipe-item>
                <div class="page bj2 fc" :class="{blur:show}">
                    <img src="img/2.png" class="main">
                    <img src="img/button3.png" class="button3" @click="next">
                </div>
            </van-swipe-item>
            <van-swipe-item>
                <div class="page bj2 fc" :class="{blur:show}">
                    <img src="img/3.png" class="main">
                    <img src="img/button4.png" class="button3" @click="next">
                </div>
            </van-swipe-item>
            <van-swipe-item>
                <div class="page bj2 fc" :class="{blur:show}">
                    <img src="img/4.png" class="main">
                    <img src="img/button5.png" class="button3" @click="next">
                </div>
            </van-swipe-item>
            <van-swipe-item>
                <div class="page bj2 fc" :class="{blur:show}">
                    <img src="img/stit.png" class="stit">
                    <div class="form">
                        <div class="form_item required">
                            <label>
                                <span>姓</span>
                                <span>名</span>
                            </label>
                            <input type="text" v-model="form.name">
                        </div>
                        <div class="form_item required">
                            <label>
                                <span>性</span>
                                <span>别</span>
                            </label>
                            <select v-model="form.sex">
                                <option value="男">男</option>
                                <option value="女">女</option>
                            </select>
                        </div>
                        <div class="form_item required">
                            <label>
                                <span>年</span>
                                <span>龄</span>
                            </label>
                            <input type="number" v-model="form.age">
                        </div>
                        <div class="form_item required">
                            <label>
                                <span>联</span>
                                <span>系</span>
                                <span>方</span>
                                <span>式</span>
                            </label>
                            <input type="text" v-model="form.phone">
                        </div>
                        <div class="form_item" :class="form.work?'':'not_required'">
                            <label>
                                <span>工</span>
                                <span>作</span>
                                <span>经</span>
                                <span>验</span>
                            </label>
                            <textarea type="text" v-model="form.work" @input="autoResizeTextarea" @focus="autoResizeTextarea" placeholder=""></textarea>
                        </div>
                    </div>
                    <div class="upload">
                        <div class="add_area" @click="selectImage">
                            <div class="cross" v-if="!form.image">
                                <p class="p1">点击上传高考准考证/高中毕业证/大学毕业证</p>
                            </div>
                            <img :src="form.image" class="image" v-if="form.image">
                        </div>
                    </div>
                    <img src="img/button6.png" class="button6" @click="submit">
                </div>
            </van-swipe-item>
        </van-swipe>
        <transition name="van-fade">
            <div class="mask fc" v-if="show===1">
                <div class="popup popup1">
                    <img src="img/button7.png" class="back" @click="reload">
                </div>
            </div>
        </transition>
    </div>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/jquery-3.6.0.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/howler.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook1.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook2.js"></script>
    <script>
        const { createApp, ref, watch, nextTick, computed } = Vue
    </script>
    <script>
        window.startData = {
            endtime: '{$endtime}',
            form:{
                name:'{$name}',
                sex:'{$sex}',
                age:'{$age}',
                phone:'{$phone}',
                work:'{$work|raw}',
                image:'{$image}'
            }
        }
        const app = createApp({
            setup() {
                const { on, bgClick } = useBgMusic('124.mp3')//调用景音乐
                setMockPage && setMockPage()//添加案例提示语
                const page = ref(1) //控制页面
                const show = ref(0) //控制弹窗
                const { userInfo, endtime } = startData
                const opportunity = ref((+startData.jihui)) //控制机会
                const start = () => {
                    if (endtime === '1') return vantAlert('活动未开始')
                    if (endtime === '2') return vantAlert('活动已结束')
                    swipeRef.value.swipeTo(1)
                }
                const edit = () => {
                    swipeRef.value.swipeTo(4)
                }
                const swipeRef = ref(null);
                const next = () => {
                    swipeRef.value.next()
                }
                const form = ref({})

                // 自适应textarea高度
                const autoResizeTextarea = (event) => {
                    const textarea = event.target;

                    // 如果内容为空，保持默认高度
                    if (!textarea.value.trim()) {
                        textarea.style.height = '6vw';
                        textarea.style.overflow = 'hidden';
                        return;
                    }

                    // 重置高度以获取正确的scrollHeight
                    textarea.style.height = '6vw';
                    // 计算vw单位：1vw = window.innerWidth / 100
                    const vwToPx = window.innerWidth / 100;
                    const minHeight = 6 * vwToPx; // 6vw
                    const maxHeight = 20 * vwToPx; // 20vw

                    // 设置新高度，限制在最小和最大值之间
                    const newHeight = Math.max(minHeight, Math.min(textarea.scrollHeight, maxHeight));
                    textarea.style.height = newHeight + 'px';

                    // 如果内容超出最大高度，显示滚动条
                    if (textarea.scrollHeight > maxHeight) {
                        textarea.style.overflow = 'auto';
                    } else {
                        textarea.style.overflow = 'hidden';
                    }
                }
                const beforeRead = async (file) => {
                    // 检查文件大小是否超过5M
                    const maxSize = 3 * 1024 * 1024; // 5MB
                    if (file.size <= maxSize) {
                        return file;
                    }

                    try {
                        // 记录开始时间
                        const startTime = Date.now();

                        // 显示loading
                        vant.showLoadingToast({
                            message: '压缩中...',
                            forbidClick: true,
                            duration: 0
                        });

                        // 创建图片对象
                        const img = await new Promise((resolve, reject) => {
                            const reader = new FileReader();
                            reader.onload = (e) => {
                                const img = new Image();
                                img.onload = () => resolve(img);
                                img.onerror = reject;
                                img.src = e.target.result;
                            };
                            reader.onerror = reject;
                            reader.readAsDataURL(file);
                        });

                        // 创建canvas进行压缩
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');
                        canvas.width = img.width;
                        canvas.height = img.height;
                        ctx.drawImage(img, 0, 0, img.width, img.height);

                        // 使用原始图片的类型，如果无法获取则默认使用 jpeg
                        const mimeType = file.type || 'image/jpeg';

                        // 使用二分查找法寻找合适的压缩质量
                        let left = 0.1;
                        let right = 0.9;
                        let lastValidBlob = null;
                        let compressionCount = 0;

                        while (left <= right && compressionCount < 10) {
                            compressionCount++;
                            const mid = (left + right) / 2;
                            const blob = await new Promise(resolve => {
                                canvas.toBlob(resolve, mimeType, mid);
                            });

                            if (blob.size <= maxSize) {
                                lastValidBlob = blob;
                                left = mid + 0.1;
                            } else {
                                right = mid - 0.1;
                            }
                        }

                        // 如果二分查找没找到结果，使用最低质量
                        if (!lastValidBlob) {
                            lastValidBlob = await new Promise(resolve => {
                                canvas.toBlob(resolve, mimeType, 0.1);
                            });
                        }

                        // 计算压缩用时
                        const endTime = Date.now();
                        const compressTime = endTime - startTime;

                        // 关闭loading
                        vant.closeToast();

                        // 如果压缩后仍然超过大小限制
                        if (lastValidBlob.size > maxSize) {
                            vant.showToast('图片太大，请选择较小的图片');
                            return false;
                        }

                        // 创建新的文件对象，保持原始文件名和类型
                        const compressedFile = new File([lastValidBlob], file.name, {
                            type: mimeType,
                            lastModified: Date.now()
                        });

                        // 输出压缩信息
                        console.log(`图片压缩信息:
                            初始大小: ${(file.size / 1024 / 1024).toFixed(2)}MB
                            最终大小: ${(compressedFile.size / 1024 / 1024).toFixed(2)}MB
                            压缩率: ${((file.size - compressedFile.size) / file.size * 100).toFixed(2)}%
                            压缩次数: ${compressionCount}次
                            原始类型: ${file.type}
                            压缩后类型: ${compressedFile.type}
                            压缩用时: ${compressTime}毫秒`);

                        // 显示压缩结果
                        vant.showSuccessToast(`压缩完成，用时${(compressTime/1000).toFixed(1)}秒`);

                        return compressedFile;
                    } catch (err) {
                        // 关闭loading
                        vant.closeToast();
                        console.error('压缩图片失败:', err);
                        vant.showToast('图片压缩失败，请重试');
                        return false;
                    }
                }
                const selectImage = () => {
                    // 创建一个隐藏的文件输入元素
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = 'image/*';
                    input.style.display = 'none';

                    // 监听文件选择事件
                    input.addEventListener('change', (e) => {
                        const file = e.target.files[0];
                        if (file) {
                            // 验证文件类型
                            if (!file.type.startsWith('image/')) {
                                vantAlert('请选择图片文件');
                                return;
                            }

                            // 验证文件大小（限制为5MB）
                            if (file.size > 5 * 1024 * 1024) {
                                vantAlert('图片大小不能超过5MB');
                                return;
                            }

                            // 创建FileReader来预览图片
                            const reader = new FileReader();
                            reader.onload = (e) => {
                                form.value.image = e.target.result; // 设置预览图片的base64数据
                            };
                            reader.readAsDataURL(file);

                            // 同时保存原始文件对象用于上传
                            form.value.imageFile = file;
                        }
                    });

                    // 触发文件选择对话框
                    input.click();
                }
                const submit = throttle(async () => {
                    if(!form.value.name) return vantAlert('请输入姓名')
                    if(!form.value.sex) return vantAlert('请选择性别')
                    if(!form.value.age) return vantAlert('请输入年龄')
                    if(!form.value.phone) return vantAlert('请输入联系方式')
                    if(!(/^(?:(?:\+|00)86)?1\d{10}$/.test(form.value.phone))){ vantAlert('请填写正确的手机号');return false}
                    const fd = new FormData()
                    fd.append('name',form.value.name)
                    fd.append('sex',form.value.sex)
                    fd.append('age',form.value.age)
                    fd.append('phone',form.value.phone)
                    if(form.value.work){
                        fd.append('work',form.value.work)
                    }
                    if(form.value.image){
                        fd.append('image',form.value.imageFile)
                    }
                    const res = await defaultHttp('submit', fd, { status: 1})
                    if (res.status === 1) {
                        show.value = 1
                    } else {
                        vantAlert(res.msg, reload)
                    }
                })
                const reload = () => {
                    window.location.reload()
                }
                const showTip = () => {
                    vantAlert('请先填写报名信息')
                }
                return {
                    startData, page, show,
                    on, bgClick,
                    swipeRef,
                    start,
                    next,
                    edit,
                    form,
                    autoResizeTextarea,
                    selectImage,
                    submit,
                    reload,
                    showTip
                }
            },
        });
        app.use(vant);
        app.mount('#app');
    </script>
    <!--分享-->
    {include file="share"/}
</body>
</html>