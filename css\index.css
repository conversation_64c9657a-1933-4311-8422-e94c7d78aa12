@font-face {
  font-family: '思源宋体';
  src: url(https://ztimg.hefei.cc/static/common/fonts/思源宋体.otf);
}
@font-face {
  font-family: 'HANCHANSHUTI';
  src: url(../HANCHANSHUTI·LONGCANG.OTF);
}
body,
div,
h1,
h2,
h3,
h4,
h5,
h6,
html,
li,
p,
span {
  font-size: 3.5vw;
}
.musicbtn {
  width: 9.6vw;
  height: 9.6vw;
  top: 3.6vw;
  right: 2.8vw;
  background-image: url(../img/music.png);
  z-index: 11;
}
.logo {
  width: 37.8667vw;
  position: absolute;
  top: 6.6vw;
  left: 2.8vw;
  z-index: 11;
}
.warp {
  margin: 0 auto;
  min-height: 170vw;
  height: 100vh;
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: '思源宋体';
  background: linear-gradient(#107cb0, #48cdf4);
}
.warp .swipe_container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.warp .page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  color: #fff;
  background: url(../img/bj.jpg) no-repeat center center / 100% auto;
}
.warp .page .title {
  margin-top: 5vw;
  margin-right: 0vw;
  width: 91.0667vw;
  z-index: 2;
}
.warp .page .title2 {
  width: 100vw;
  z-index: 2;
}
.warp .page .button {
  margin-top: 4vw;
  width: 45.7333vw;
}
.warp .page .main {
  width: 100vw;
}
.warp .page .button3 {
  width: 45.7333vw;
  position: absolute;
  bottom: 6vh;
}
.warp .page .stit {
  width: 74.4vw;
}
.warp .page .form {
  margin-top: -10vw;
  width: 80vw;
  min-height: 67.067vw;
  padding-bottom: 6vw;
  background: #FFF8D9;
  border-radius: 3.6vw;
  box-shadow: 1.333vw 1.333vw 0 rgba(3, 133, 178, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.warp .page .form .form_item {
  margin-top: 6vw;
  width: 65.067vw;
  border: 1px solid #1B1B1B;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}
.warp .page .form .form_item label {
  color: #0086AC;
  font-weight: bold;
  flex: 1;
  border-right: 1px solid #1B1B1B;
  background: #E8F8FF;
  display: flex;
  align-items: center;
  justify-content: space-around;
  text-align: justify;
  text-align-last: justify;
  height: 100%;
  padding: 0 1vw;
}
.warp .page .form .form_item select,
.warp .page .form .form_item input {
  width: 46.267vw;
  height: 6vw;
  flex-shrink: 0;
  color: #000;
  border: none;
  outline: none;
  background: none;
  text-align: center;
  padding: 0 2vw;
}
.warp .page .form .form_item textarea {
  width: 46.267vw;
  height: 6vw;
  min-height: 6vw;
  max-height: 30vw;
  flex-shrink: 0;
  color: #000;
  border: none;
  outline: none;
  background: none;
  text-align: center;
  resize: none;
  overflow: hidden;
  line-height: 1.4;
  padding: 0 2vw;
  box-sizing: border-box;
}
.warp .page .form .form_item select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.warp .page .form .form_item.required::before {
  content: '*';
  font-size: 4vw;
  font-weight: bold;
  color: red;
  position: absolute;
  right: 2vw;
}
.warp .page .form .form_item.not_required::before {
  content: '选填';
  color: #B3AFAF;
  position: absolute;
  right: 2vw;
}
.warp .page .form2 {
  margin-top: -17vw;
  width: 80vw;
  min-height: 67.067vw;
  padding-bottom: 6vw;
  background: #FFF8D9;
  border-radius: 3.6vw;
  box-shadow: 1.333vw 1.333vw 0 rgba(3, 133, 178, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.warp .page .form2 .stit2 {
  margin-top: 6vw;
  margin-bottom: 2vw;
  width: 31.3333vw;
}
.warp .page .form2 .form_item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 8.6667vw;
}
.warp .page .form2 .form_item label {
  width: 16.667vw;
  height: 4.533vw;
  background: #FFA200;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.warp .page .form2 .form_item label span {
  font-size: 3vw;
}
.warp .page .form2 .form_item .content {
  width: 45.2vw;
  min-height: 6.6667vw;
  max-height: 20vw;
  overflow-y: auto;
  border-bottom: 1px solid #000;
  margin-left: 6vw;
  color: #000;
  display: flex;
  justify-content: center;
  font-weight: bold;
  text-align: justify;
}
.warp .page .upload {
  margin-top: 4vw;
  width: 80vw;
  height: 35.6vw;
  background: #FFF8D9;
  box-shadow: 1.333vw 1.333vw 0 rgba(3, 133, 178, 0.9);
  border-radius: 3.6vw;
  border: 1px solid #1B1B1B;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.warp .page .upload .add_area {
  width: 76.533vw;
  height: 33.067vw;
  background: #FFFDF4;
  border-radius: 3.6vw;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.warp .page .upload .add_area .cross {
  margin-top: -5vw;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}
.warp .page .upload .add_area .cross::before {
  content: '';
  position: absolute;
  width: 9.867vw;
  height: 2.133vw;
  background: #B3AFAF;
}
.warp .page .upload .add_area .cross::after {
  content: '';
  position: absolute;
  width: 2.133vw;
  height: 9.867vw;
  background: #B3AFAF;
}
.warp .page .upload .add_area .cross .p1 {
  margin-top: 25vw;
  color: #B3AFAF;
  font-size: 3vw;
}
.warp .page .upload .add_area .image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.warp .page .button6 {
  width: 37.6vw;
  margin-top: 6vw;
}
.warp .bj2 {
  background: url(../img/bj2.jpg) no-repeat center center / 100% auto;
}
.blur {
  filter: blur(1vw);
}
.fc {
  justify-content: center;
}
.mask {
  z-index: 10;
  position: fixed;
  top: 0;
  left: 0;
  min-height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(0, 0, 0, 0.6);
  transform: translateX(-50%);
  left: 50%;
  color: #fff;
}
.mask .popup {
  margin-top: -1vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.mask .popup .back {
  width: 29.8667vw;
  position: absolute;
  bottom: -15vw;
}
.mask .popup1 {
  margin-top: -10vw;
  width: 75.3333vw;
  height: 66.5333vw;
  background: url(../img/popup1.png) no-repeat center top / 100% 100%;
}
.blink-2 {
  animation: blink-2 1s linear infinite both;
}
@keyframes blink-2 {
  0%,
  10%,
  90%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.2;
  }
}
@keyframes flash {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes flash2 {
  0%,
  99% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes sc {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(0.7) translateY(-10vw);
  }
}
@keyframes pulsate-bck2 {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
